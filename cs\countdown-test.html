<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>倒计时颜色测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .countdown-display {
            font-size: 48px;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 10px;
        }
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .btn-green { background-color: #28A745; color: white; }
        .btn-yellow { background-color: #FFB800; color: white; }
        .btn-red { background-color: #FF4444; color: white; }
        .description {
            margin: 20px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .color-rule {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .green-rule { background-color: #d4edda; border-left: 4px solid #28A745; }
        .yellow-rule { background-color: #fff3cd; border-left: 4px solid #FFB800; }
        .red-rule { background-color: #f8d7da; border-left: 4px solid #FF4444; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>倒计时颜色区分测试</h1>
        
        <div class="description">
            <h3>颜色规则：</h3>
            <div class="color-rule green-rule">
                <strong>绿色</strong>：1小时以上剩余时间
            </div>
            <div class="color-rule yellow-rule">
                <strong>黄色</strong>：30分钟到1小时剩余时间
            </div>
            <div class="color-rule red-rule">
                <strong>红色</strong>：30分钟内剩余时间
            </div>
        </div>
        
        <div class="countdown-display" id="countdown-timer">02:00:00</div>
        
        <div class="controls">
            <button class="btn btn-green" onclick="setTime(7200)">设置2小时 (绿色)</button>
            <button class="btn btn-yellow" onclick="setTime(3000)">设置50分钟 (黄色)</button>
            <button class="btn btn-red" onclick="setTime(1200)">设置20分钟 (红色)</button>
            <button class="btn" onclick="startCountdown()" style="background-color: #007bff; color: white;">开始倒计时</button>
            <button class="btn" onclick="stopCountdown()" style="background-color: #6c757d; color: white;">停止倒计时</button>
        </div>
    </div>

    <script>
        let timer = null;
        let totalSeconds = 2 * 60 * 60; // 默认2小时

        function updateCountdownColor(seconds) {
            const $timer = document.getElementById('countdown-timer');

            if (seconds < 30 * 60) { // 30分钟内 - 红色
                $timer.style.color = '#FF4444';
            } else if (seconds < 60 * 60) { // 1小时内 - 黄色
                $timer.style.color = '#FFB800';
            } else { // 1小时以上（包含1小时） - 绿色
                $timer.style.color = '#28A745';
            }
        }

        function updateDisplay() {
            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);
            const seconds = totalSeconds % 60;
            
            const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            document.getElementById('countdown-timer').textContent = timeStr;
            
            updateCountdownColor(totalSeconds);
        }

        function startCountdown() {
            if (timer) {
                clearInterval(timer);
            }
            
            timer = setInterval(function() {
                updateDisplay();
                totalSeconds--;
                
                if (totalSeconds < 0) {
                    clearInterval(timer);
                    document.getElementById('countdown-timer').textContent = '00:00:00';
                    document.getElementById('countdown-timer').style.color = '#FF4444';
                    timer = null;
                }
            }, 1000);
        }

        function stopCountdown() {
            if (timer) {
                clearInterval(timer);
                timer = null;
            }
        }

        function setTime(seconds) {
            totalSeconds = seconds;
            updateDisplay();
        }

        // 初始化显示
        updateDisplay();
    </script>
</body>
</html>
