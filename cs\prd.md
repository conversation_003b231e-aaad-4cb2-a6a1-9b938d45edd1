### 需求1(所有数据来源与后端接口)
1. 布局参考./first.png，风格参考**./参考.png**
2. 字段要求
   1. 客户名展示为当前的门店名字
   2. 已分拣单品数:展示当天当前门店已分拣的货品种类数
   3. 未分拣单品数:展示当天当前门店未分拣的货品种类数
   4. 总任务完成进度:已分拣单品数/(已分拣+未分拣)*100%保留2位小数百分比
   5. 二维码，二维码内容是{"lsid":"xx","ldid":"x"}的json字符串
   6. 配送倒计时:暂时固定为`00:30:00`,来源于后端接口
   7. 配送线路:当前门店所属线路
   8. 配送司机: 线路绑定的司机,多个以/分割，例如`张三/李四`

### 需求2
- 布局参考./second.png,风格参考**./参考.png**
2. 字段要求(所有数据来源与后端接口接口)
   1. 线路包含的门店:多个门店名字滚动展示，注意需要**滚动展示**
   2. 已分拣单品数:展示当天已分拣的货品种类数
   3. 未分拣单品数:展示当天未分拣的货品种类数
   4. 总任务完成进度:已分拣单品数/(已分拣+未分拣)*100%保留2位小数百分比
   5. 二维码，二维码内容是{"lcid":"xx"}的json字符串,lcid是线路id
   6. 配送倒计时:暂时固定为`00:30:00`,来源于后端接口
   7. 配送线路:当前线路
   8. 配送司机: 线路绑定的司机,多个以/分割，例如`张三/李四`

### 交互方式
- 首页入口 url/cs
- 首页下拉框
  - 投屏方式(默认门店)
    - 门店
      - 系统有效的门店
    - 线路
      - 当前系统有效的线路
  - 门店下拉框(投屏方式选择门店的时候展示)
  - 线路下拉框(投屏方式选择线路的时候展示)
  - 确定按钮
    - 点击确定后进入投屏页面，投屏页面可以返回首页，也可以透出投屏

### 投屏要求，必须严格适配
- 投屏尺寸：长54cm*高32cm
- 分辨率（刷新率） 1920*1080 75HZ
- 点间距 233.1um*233.1um

### 注意
- 数据不要在前端写成静态的，要后端提供
- 所有代码生成在cs目录下面，不扩展到其它地方
- 引用的js文件使用本地现有的