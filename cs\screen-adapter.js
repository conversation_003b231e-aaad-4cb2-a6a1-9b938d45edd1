/**
 * 投屏适配脚本
 * 智能适配不同分辨率，确保页面在投屏和普通浏览器下都能正常显示
 */

(function() {
    'use strict';
    
    // 目标分辨率
    const TARGET_WIDTH = 1920;
    const TARGET_HEIGHT = 1080;
    
    // 检测是否为投屏设备
    function isProjectionDevice() {
        return window.innerWidth === TARGET_WIDTH && window.innerHeight === TARGET_HEIGHT;
    }
    
    // 适配函数
    function adaptScreen() {
        const container = document.querySelector('.screen-container');
        if (!container) return;
        
        // 获取当前视口尺寸
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        
        // 如果是投屏设备（1920*1080），使用原生分辨率
        if (isProjectionDevice()) {
            container.style.transform = 'none';
            container.style.transformOrigin = 'top left';
            container.style.left = '0px';
            container.style.top = '0px';
            container.style.width = `${TARGET_WIDTH}px`;
            container.style.height = `${TARGET_HEIGHT}px`;
            container.style.position = 'fixed';
            document.body.style.overflow = 'hidden';
            container.classList.remove('auto-scale');
            return;
        }
        
        // 普通浏览器：计算缩放比例
        const scaleX = viewportWidth / TARGET_WIDTH;
        const scaleY = viewportHeight / TARGET_HEIGHT;
        const scale = Math.min(scaleX, scaleY);
        
        // 最小缩放比例，确保页面不会太小
        const minScale = 0.3;
        const finalScale = Math.max(scale, minScale);
        
        // 应用缩放
        container.style.transform = `scale(${finalScale})`;
        container.style.transformOrigin = 'top left';
        container.style.width = `${TARGET_WIDTH}px`;
        container.style.height = `${TARGET_HEIGHT}px`;
        container.classList.add('auto-scale');
        
        // 计算居中位置
        const scaledWidth = TARGET_WIDTH * finalScale;
        const scaledHeight = TARGET_HEIGHT * finalScale;
        const offsetX = (viewportWidth - scaledWidth) / 2;
        const offsetY = Math.max(0, (viewportHeight - scaledHeight) / 2);
        
        container.style.left = `${Math.max(0, offsetX)}px`;
        container.style.top = `${Math.max(0, offsetY)}px`;
        container.style.position = 'absolute';
        
        // 调整body样式
        document.body.style.overflow = scaledHeight > viewportHeight ? 'auto' : 'hidden';
        document.body.style.margin = '0';
        document.body.style.padding = '0';
        document.body.style.background = '#000';
        document.body.style.minHeight = `${Math.max(viewportHeight, scaledHeight + offsetY * 2)}px`;
    }
    
    // 禁用浏览器缩放（仅在投屏设备上）
    function disableZoom() {
        if (!isProjectionDevice()) return;
        
        // 禁用鼠标滚轮缩放
        document.addEventListener('wheel', function(e) {
            if (e.ctrlKey) {
                e.preventDefault();
            }
        }, { passive: false });
        
        // 禁用键盘缩放
        document.addEventListener('keydown', function(e) {
            if (e.ctrlKey && (e.key === '+' || e.key === '-' || e.key === '0')) {
                e.preventDefault();
            }
        });
        
        // 禁用触摸缩放
        document.addEventListener('touchstart', function(e) {
            if (e.touches.length > 1) {
                e.preventDefault();
            }
        }, { passive: false });
        
        document.addEventListener('touchmove', function(e) {
            if (e.touches.length > 1) {
                e.preventDefault();
            }
        }, { passive: false });
    }
    
    // 设置viewport
    function setViewport() {
        let viewport = document.querySelector('meta[name="viewport"]');
        if (!viewport) {
            viewport = document.createElement('meta');
            viewport.name = 'viewport';
            document.head.appendChild(viewport);
        }
        
        if (isProjectionDevice()) {
            // 投屏设备：固定viewport
            viewport.content = 'width=1920, height=1080, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no';
        } else {
            // 普通浏览器：允许缩放
            viewport.content = 'width=device-width, initial-scale=1.0, user-scalable=yes';
        }
    }
    
    // 页面加载完成后执行
    function initialize() {
        setViewport();
        adaptScreen();
        disableZoom();
        
        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            setViewport();
            adaptScreen();
        });
        
        // 监听页面方向变化
        window.addEventListener('orientationchange', function() {
            setTimeout(function() {
                setViewport();
                adaptScreen();
            }, 100);
        });
        
        // 定期检查适配状态（仅投屏设备）
        if (isProjectionDevice()) {
            setInterval(adaptScreen, 5000);
        }
    }
    
    // 等待DOM加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
    // 导出适配函数，供外部调用
    window.screenAdapter = {
        adapt: adaptScreen,
        setViewport: setViewport,
        isProjection: isProjectionDevice
    };
})(); 