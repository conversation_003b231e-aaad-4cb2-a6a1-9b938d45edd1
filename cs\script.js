$(document).ready(function() {
    // 存储原始数据
    let originalStores = [];
    let originalRoutes = [];
    
    // 初始化页面
    init();
    
    function init() {
        // 绑定事件
        bindEvents();
        
        // 初始化数据
        loadInitialData();
    }
    
    function bindEvents() {
        // 投屏方式选择事件
        $('#screen-type').change(function() {
            const selectedType = $(this).val();
            handleScreenTypeChange(selectedType);
        });
        
        // 确定按钮点击事件
        $('#confirm-btn').click(function() {
            handleConfirm();
        });
        
        // 门店输入框事件
        $('#store-input').on('focus', function() {
            showDropdown('store-list');
        }).on('input', function() {
            filterDropdown('store-list', $(this).val(), originalStores);
        });
        
        // 线路输入框事件
        $('#route-input').on('focus', function() {
            showDropdown('route-list');
        }).on('input', function() {
            filterDropdown('route-list', $(this).val(), originalRoutes);
        });
        
        // 点击文档其他地方关闭下拉框
        $(document).click(function(e) {
            if (!$(e.target).closest('.custom-select-container').length) {
                hideAllDropdowns();
            }
        });
    }
    
    function handleScreenTypeChange(type) {
        // 隐藏所有选择框
        $('#store-group').hide();
        $('#route-group').hide();
        
        // 清空输入
        $('#store-input').val('');
        $('#route-input').val('');
        
        if (type === 'store') {
            $('#store-group').show();
            loadStores();
        } else if (type === 'route') {
            $('#route-group').show();
            loadRoutes();
        }
    }
    
    function loadInitialData() {
        // 默认选择门店投屏方式
        $('#screen-type').val('store').trigger('change');
        console.log('投屏系统初始化完成');
    }
    
    function loadStores() {
        // 加载门店列表
        $.ajax({
            url: 'api.php',
            type: 'GET',
            dataType: 'json',
            data: {
                do: 'getStores'
            },
            success: function(response) {
                console.log('门店API响应:', response);
                if (response.success) {
                    populateStoreSelect(response.data);
                } else {
                    console.error('加载门店失败:', response.message);
                    alert('加载门店失败: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('请求失败:', error, xhr.responseText);
                alert('请求失败，请检查网络连接');
            }
        });
    }
    
    function loadRoutes() {
        // 加载线路列表
        $.ajax({
            url: 'api.php',
            type: 'GET',
            dataType: 'json',
            data: {
                do: 'getRoutes'
            },
            success: function(response) {
                console.log('线路API响应:', response);
                if (response.success) {
                    populateRouteSelect(response.data);
                } else {
                    console.error('加载线路失败:', response.message);
                    alert('加载线路失败: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('请求失败:', error, xhr.responseText);
                alert('请求失败，请检查网络连接');
            }
        });
    }
    
    function populateStoreSelect(stores) {
        // 存储原始数据
        originalStores = stores;
        
        const $dropdown = $('#store-list');
        $dropdown.empty();
        
        stores.forEach(function(store) {
            const $item = $('<div class="dropdown-item"></div>')
                .text(store.name)
                .data('id', store.id)
                .data('name', store.name);
            
            $item.click(function() {
                $('#store-input').val(store.name);
                hideDropdown('store-list');
            });
            
            $dropdown.append($item);
        });
        
        // 清空输入框
        $('#store-input').val('');
    }
    
    function populateRouteSelect(routes) {
        // 存储原始数据
        originalRoutes = routes;
        
        const $dropdown = $('#route-list');
        $dropdown.empty();
        
        routes.forEach(function(route) {
            const $item = $('<div class="dropdown-item"></div>')
                .text(route.name)
                .data('id', route.id)
                .data('name', route.name);
            
            $item.click(function() {
                $('#route-input').val(route.name);
                hideDropdown('route-list');
            });
            
            $dropdown.append($item);
        });
        
        // 清空输入框
        $('#route-input').val('');
    }
    
    function handleConfirm() {
        const screenType = $('#screen-type').val();
        
        if (!screenType) {
            alert('请选择投屏方式');
            return;
        }
        
        let targetId = '';
        let targetName = '';
        
        if (screenType === 'store') {
            targetName = $('#store-input').val();
            
            if (!targetName) {
                alert('请选择门店');
                return;
            }
            
            // 从原始数据中找到对应的ID
            const store = originalStores.find(s => s.name === targetName);
            if (!store) {
                alert('请从列表中选择有效的门店');
                return;
            }
            targetId = store.id;
            
            // 跳转到门店投屏页面
            window.location.href = `store.html?id=${targetId}&name=${encodeURIComponent(targetName)}`;
            
        } else if (screenType === 'route') {
            targetName = $('#route-input').val();
            
            if (!targetName) {
                alert('请选择线路');
                return;
            }
            
            // 从原始数据中找到对应的ID
            const route = originalRoutes.find(r => r.name === targetName);
            if (!route) {
                alert('请从列表中选择有效的线路');
                return;
            }
            targetId = route.id;
            
            // 跳转到线路投屏页面
            window.location.href = `route.html?id=${targetId}&name=${encodeURIComponent(targetName)}`;
        }
    }
    
    // 下拉框相关函数
    function showDropdown(dropdownId) {
        hideAllDropdowns();
        $('#' + dropdownId).addClass('show');
    }
    
    function hideDropdown(dropdownId) {
        $('#' + dropdownId).removeClass('show');
    }
    
    function hideAllDropdowns() {
        $('.dropdown-list').removeClass('show');
    }
    
    function filterDropdown(dropdownId, searchText, originalData) {
        const $dropdown = $('#' + dropdownId);
        $dropdown.empty();
        
        // 过滤数据
        const filteredData = originalData.filter(function(item) {
            return item.name.toLowerCase().includes(searchText.toLowerCase());
        });
        
        // 重新填充下拉框
        filteredData.forEach(function(item) {
            const $item = $('<div class="dropdown-item"></div>')
                .text(item.name)
                .data('id', item.id)
                .data('name', item.name);
            
            $item.click(function() {
                if (dropdownId === 'store-list') {
                    $('#store-input').val(item.name);
                } else if (dropdownId === 'route-list') {
                    $('#route-input').val(item.name);
                }
                hideDropdown(dropdownId);
            });
            
            $dropdown.append($item);
        });
        
        // 显示下拉框
        $dropdown.addClass('show');
    }
    
    // 工具函数
    function formatTime(date) {
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');
        return `${hours}:${minutes}:${seconds}`;
    }
    
    function formatDate(date) {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
}); 