$(document).ready(function() {
    // 获取URL参数
    const urlParams = new URLSearchParams(window.location.search);
    const storeId = urlParams.get('id');
    const storeName = urlParams.get('name');
    
    // 初始化页面
    init();
    
    function init() {
        // 显示门店名称
        if (storeName) {
            $('#store-name').text(decodeURIComponent(storeName));
        }
        
        // 开始更新时间
        updateTime();
        setInterval(updateTime, 1000);
        
        // 开始倒计时
        startCountdown();
        
        // 加载门店数据
        loadStoreData(storeId);
        
        // 定时刷新数据
        setInterval(function() {
            loadStoreData(storeId);
        }, 30000); // 30秒刷新一次
    }
    
    function updateTime() {
        const now = new Date();
        const timeStr = formatTime(now);
        const dateStr = formatDate(now);
        $('#current-time').text(`${dateStr} ${timeStr}`);
    }
    
    function startCountdown() {
        // 从HTML中读取初始时间
        const initialTime = $('#countdown-timer').text();
        let totalSeconds = parseTimeToSeconds(initialTime);

        const timer = setInterval(function() {
            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);
            const seconds = totalSeconds % 60;

            const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            $('#countdown-timer').text(timeStr);

            // 根据剩余时间设置颜色
            updateCountdownColor(totalSeconds);

            totalSeconds--;

            if (totalSeconds < 0) {
                clearInterval(timer);
                $('#countdown-timer').text('00:00:00');
                $('#countdown-timer').css('color', '#FF4444');
            }
        }, 1000);
    }

    function updateCountdownColor(totalSeconds) {
        const $timer = $('#countdown-timer');

        if (totalSeconds <= 30 * 60) { // 30分钟内 - 红色
            $timer.css('color', '#FF4444');
        } else if (totalSeconds <= 60 * 60) { // 1小时内 - 黄色
            $timer.css('color', '#FFB800');
        } else { // 1小时以上 - 绿色
            $timer.css('color', '#28A745');
        }
    }
    
    function loadStoreData(storeId) {
        $.ajax({
            url: 'api.php',
            type: 'GET',
            dataType: 'json',
            data: {
                do: 'getStoreData',
                storeId: storeId
            },
            success: function(response) {
                console.log('门店数据API响应:', response);
                if (response.success) {
                    updateStoreDisplay(response.data);
                } else {
                    console.error('加载门店数据失败:', response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('请求失败:', error, xhr.responseText);
            }
        });
    }
    
    function updateStoreDisplay(data) {
        // 更新基本信息
        $('#store-name').text(data.storeName || '未知门店');
        $('#sorted-count').text(data.sortedCount || 0);
        $('#unsorted-count').text(data.unsortedCount || 0);
        $('#delivery-route').text(data.deliveryRoute || '未分配');
        $('#delivery-driver').text(data.deliveryDriver || '未分配');
        
        // 计算并更新进度
        const sortedCount = parseInt(data.sortedCount) || 0;
        const unsortedCount = parseInt(data.unsortedCount) || 0;
        const totalCount = sortedCount + unsortedCount;
        
        let progressPercent = 0;
        if (totalCount > 0) {
            progressPercent = (sortedCount / totalCount) * 100;
        }
        
        updateProgress(progressPercent);
        
        // 更新二维码
        updateQRCode(data.qrCodeData);
    }
    
    function updateProgress(percent) {
        const formattedPercent = percent.toFixed(2);
        $('#progress-fill').css('width', percent + '%');
        $('#progress-text').text(formattedPercent + '%');
    }
    
    function updateQRCode(qrData) {
        // 清空二维码容器
        $('#qr-code').empty();
        
        if (qrData && qrData.lsid && qrData.ldid) {
            // 生成二维码
            const qrString = JSON.stringify(qrData);
            // 根据屏幕分辨率调整二维码尺寸
            const qrSize = 500;
            const qrcode = new QRCode(document.getElementById('qr-code'), {
                text: qrString,
                width: qrSize,
                height: qrSize,
                colorDark: '#000000',
                colorLight: '#ffffff',
                correctLevel: QRCode.CorrectLevel.M
            });
        } else {
            // 显示默认二维码
            const defaultQR = JSON.stringify({lsid: "demo", ldid: "demo"});
            const qrSize = 500;
            const qrcode = new QRCode(document.getElementById('qr-code'), {
                text: defaultQR,
                width: qrSize,
                height: qrSize,
                colorDark: '#000000',
                colorLight: '#ffffff',
                correctLevel: QRCode.CorrectLevel.M
            });
        }
    }

    // 解析时间字符串为秒数
    function parseTimeToSeconds(timeStr) {
        const parts = timeStr.split(':');
        if (parts.length !== 3) return 0;

        const hours = parseInt(parts[0]) || 0;
        const minutes = parseInt(parts[1]) || 0;
        const seconds = parseInt(parts[2]) || 0;

        return hours * 3600 + minutes * 60 + seconds;
    }

    // 工具函数
    function formatTime(date) {
        const hours = date.getHours().toString().padStart(2, '0');
        const minutes = date.getMinutes().toString().padStart(2, '0');
        const seconds = date.getSeconds().toString().padStart(2, '0');
        return `${hours}:${minutes}:${seconds}`;
    }
    
    function formatDate(date) {
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
}); 