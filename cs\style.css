/* 通用样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
}

/* 投屏页面动态适配 - 支持不同分辨率 */
.screen-container {
    width: 1920px;
    height: 1080px;
    transform-origin: 0 0;
    overflow: hidden;
    position: relative;
}

/* 动态缩放函数 - 通过JavaScript调用 */
.screen-container.auto-scale {
    transform-origin: 0 0;
    position: relative;
}

/* 专门针对1920*1080投屏的优化 */
@media screen and (width: 1920px) and (height: 1080px) {
    html, body {
        width: 1920px !important;
        height: 1080px !important;
        overflow: hidden;
    }
    
    .screen-container {
        width: 1920px !important;
        height: 1080px !important;
        position: fixed;
        top: 0;
        left: 0;
        transform: none !important;
        zoom: 1 !important;
    }
}

/* 防止页面缩放和变形的额外保护 */
html {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    -moz-text-size-adjust: 100%;
    text-size-adjust: 100%;
}

body {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
    margin: 0;
    padding: 0;
    background: #000;
}

/* 普通浏览器下的体验优化 */
@media screen and (max-width: 1920px), screen and (max-height: 1080px) {
    body {
        overflow: auto;
        background: #000;
    }
    
    .screen-container {
        position: relative;
        margin: 0 auto;
        box-shadow: 0 0 20px rgba(0,0,0,0.5);
    }
}

/* 小屏幕设备的额外优化 */
@media screen and (max-width: 1200px) {
    body {
        padding: 10px;
    }
    
    .screen-container {
        box-shadow: 0 0 10px rgba(0,0,0,0.3);
    }
    
    /* 门店投屏在小屏幕上的优化 */
    .store-layout .store-main-content {
        flex-direction: column;
        gap: 15px;
        height: auto;
    }
    
    .left-main-section {
        order: 1;
    }
    
    .right-main-section {
        order: 2;
    }
    
    .counts-section {
        flex-direction: row;
        gap: 15px;
        height: auto;
    }
    
    .count-value {
        font-size: 3rem;
    }
    
    .store-name-value {
        font-size: 3rem;
    }
    
    .qr-section .qr-code {
        width: 280px;
        height: 280px;
    }
    
    /* 线路投屏在小屏幕上的优化 */
    .route-layout .route-main-content {
        flex-direction: column;
        gap: 15px;
        height: auto;
    }
    
    .route-left-section {
        order: 1;
    }
    
    .route-right-section {
        order: 2;
    }
    
    .route-counts-section {
        flex-direction: row;
        gap: 15px;
        height: auto;
    }
    
    .route-qr-section .qr-code {
        width: 280px;
        height: 280px;
    }
    
    .route-left-section .route-stores-container {
        height: 200px;
    }
    
    .route-stores-label {
        font-size: 1rem;
    }
    
    .route-store-item {
        font-size: 1rem;
        padding: 8px 12px;
    }
}

/* 中等屏幕设备优化 */
@media screen and (max-width: 1600px) and (min-width: 1201px) {
    .screen-container {
        box-shadow: 0 0 15px rgba(0,0,0,0.4);
    }
    
    .store-layout .qr-section .qr-code {
        width: 550px;
        height: 550px;
    }
    
    .count-value {
        font-size: 4rem;
    }
    
    .store-name-value {
        font-size: 4rem;
    }
    
    /* 线路投屏在中等屏幕上的优化 */
    .route-layout .route-qr-section .qr-code {
        width: 550px;  /* 适应新的区域大小 */
        height: 550px; /* 适应新的区域大小 */
    }
    
    .route-count-card .count-value {
        font-size: 3.5rem;
    }
    
    .route-info-value {
        font-size: 1.3rem;
    }
    
    .route-info-row:first-child .route-info-value {
        font-size: 1.6rem;
    }
}

/* 确保投屏容器始终保持固定尺寸 */
.screen-container {
    box-sizing: border-box !important;
    -webkit-box-sizing: border-box !important;
    -moz-box-sizing: border-box !important;
}

.container {
    width: 100%;
    max-width: none;
    margin: 0 auto;
    padding: 2rem 1rem;
    box-sizing: border-box;
}

/* 首页样式 */
.header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.header h1 {
    font-size: 2.2rem;
    color: #2c3e50;
    margin-bottom: 0.8rem;
}

.form-container {
    background: white;
    padding: 2.5rem 1.5rem;
    border-radius: 0.7rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    max-width: 100%;
    margin: 0 auto;
}

.form-group {
    margin-bottom: 1.2rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: bold;
    color: #2c3e50;
    font-size: 1rem;
}

.form-group select,
.searchable-input {
    width: 100%;
    padding: 0.7rem 1rem;
    border: 2px solid #ddd;
    border-radius: 0.3rem;
    font-size: 1rem;
    background-color: white;
    box-sizing: border-box;
}

.form-group select:focus {
    outline: none;
    border-color: #3498db;
}

.btn {
    width: 100%;
    padding: 1rem;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 0.3rem;
    font-size: 1.1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #2980b9;
}

.btn:disabled {
    background-color: #bdc3c7;
    cursor: not-allowed;
}

/* 自定义下拉框样式 */
.custom-select-container {
    position: relative;
    width: 100%;
}

.custom-select-container .searchable-input {
    padding-right: 40px;
}

.dropdown-arrow {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
    font-size: 12px;
    pointer-events: none;
    user-select: none;
}

.dropdown-list {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 2px solid #ddd;
    border-top: none;
    border-radius: 0 0 5px 5px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.dropdown-list.show {
    display: block;
}

.dropdown-item {
    padding: 12px;
    cursor: pointer;
    border-bottom: 1px solid #eee;
    transition: background-color 0.2s;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item.selected {
    background-color: #3498db;
    color: white;
}

.dropdown-item:last-child {
    border-bottom: none;
}

/* 当输入框获得焦点时的样式 */
.custom-select-container .searchable-input:focus {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.custom-select-container .searchable-input:focus + .dropdown-arrow {
    color: #3498db;
}

/* 投屏页面样式 - 针对1920*1080分辨率优化 */
.screen-container {
    width: 1920px;
    height: 1080px;
    background: linear-gradient(135deg, #0a1433 0%, #1a2b5c 25%, #2a3f7a 50%, #1a2b5c 75%, #0a1433 100%);
    display: flex;
    flex-direction: column;
    color: white;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    overflow: hidden;
    position: relative;
}

.screen-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 20%, rgba(0, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(0, 150, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 70%, rgba(100, 200, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
}

.screen-header {
    height: 120px;
    background: linear-gradient(135deg, rgba(0, 150, 255, 0.2) 0%, rgba(0, 200, 255, 0.1) 100%);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 60px;
    border-bottom: 2px solid rgba(0, 255, 255, 0.3);
    position: relative;
    backdrop-filter: blur(10px);
}

.screen-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(0, 255, 255, 0.1) 20%, 
        rgba(0, 255, 255, 0.05) 50%, 
        rgba(0, 255, 255, 0.1) 80%, 
        transparent 100%);
    pointer-events: none;
}

.screen-title {
    font-size: 3rem;
    font-weight: bold;
    color: #00ffff;
    text-shadow: 
        0 0 10px rgba(0, 255, 255, 0.8),
        0 0 20px rgba(0, 255, 255, 0.6),
        0 0 30px rgba(0, 255, 255, 0.4);
    position: relative;
    z-index: 1;
}

.screen-time {
    font-size: 2rem;
    font-weight: bold;
    color: #66ccff;
    text-shadow: 
        0 0 5px rgba(102, 204, 255, 0.8),
        0 0 10px rgba(102, 204, 255, 0.6);
    position: relative;
    z-index: 1;
}

.screen-content {
    flex: 1;
    display: flex;
    padding: 40px 60px;
    gap: 40px;
}

.left-section {
    flex: 2;
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.right-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(255,255,255,0.1);
    border-radius: 20px;
    padding: 40px;
    backdrop-filter: blur(10px);
}

.info-card {
    background: linear-gradient(135deg, rgba(0, 50, 100, 0.3) 0%, rgba(0, 80, 150, 0.2) 100%);
    border-radius: 15px;
    padding: 30px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 255, 255, 0.3);
    position: relative;
    box-shadow: 
        0 0 20px rgba(0, 255, 255, 0.1),
        inset 0 0 20px rgba(0, 255, 255, 0.05);
}

.info-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 15px;
    background: linear-gradient(45deg, 
        transparent 0%, 
        rgba(0, 255, 255, 0.1) 25%, 
        transparent 50%, 
        rgba(0, 255, 255, 0.1) 75%, 
        transparent 100%);
    pointer-events: none;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-label {
    font-weight: bold;
    opacity: 0.9;
}

.info-value {
    font-weight: bold;
    color: #00ffff;
    text-shadow: 
        0 0 10px rgba(0, 255, 255, 0.8),
        0 0 20px rgba(0, 255, 255, 0.4);
}

.progress-container {
    margin-top: 20px;
}

.progress-bar {
    width: 100%;
    height: 30px;
    background: rgba(0, 20, 40, 0.6);
    border-radius: 15px;
    overflow: hidden;
    position: relative;
    border: 1px solid rgba(0, 255, 255, 0.3);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00ffff, #0099ff, #00ccff);
    border-radius: 15px;
    transition: width 0.5s ease;
    position: relative;
    box-shadow: 
        0 0 20px rgba(0, 255, 255, 0.6),
        inset 0 0 20px rgba(255, 255, 255, 0.1);
}

.progress-fill::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(255, 255, 255, 0.3) 50%, 
        transparent 100%);
    animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: bold;
    color: white;
    text-shadow: 
        0 1px 3px rgba(0, 0, 0, 0.8),
        0 0 5px rgba(0, 0, 0, 0.5);
    text-align: center;
    white-space: nowrap;
}

.qr-code {
    width: 500px;
    height: 500px;
    background: white;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px 0;
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.qr-code canvas {
    border-radius: 5px;
}

.countdown-timer {
    font-size: 3rem;
    font-weight: bold;
    color: #FFD700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
    margin-bottom: 20px;
}

.route-stores {
    height: 180px;
    overflow: hidden;
    position: relative;
    padding-bottom: 20px;
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    padding: 10px;
    margin-bottom: 20px;
}

.route-stores-scroll {
    position: absolute;
    top: 10px;
    width: 100%;
    transition: transform 0.5s cubic-bezier(0.4,0,0.2,1);
}

.route-store-item {
    height: 160px;
    line-height: 160px;
    font-size: 5rem;
    font-weight: bold;
    color: yellow;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin: 0;
    padding: 0 16px;
    box-sizing: border-box;
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    border-radius: 8px;
    border: 2px solid rgba(255,255,255,0.2);
    box-shadow: 0 2px 8px rgba(0,0,0,0.3), inset 0 1px 0 rgba(255,255,255,0.3);
    transition: all 0.3s ease;
    display: block;
}

.route-store-item:hover {
    background: linear-gradient(135deg, #5ba0f2 0%, #4580cd 100%);
    transform: translateY(-2px);
    box-shadow: 
        0 4px 12px rgba(0, 0, 0, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

/* 返回按钮悬停区域 */
.back-btn-area {
    position: fixed;
    top: 0;
    left: 0;
    width: 150px;
    height: 80px;
    z-index: 1000;
}

.back-btn {
    position: fixed;
    top: 20px;
    left: 20px;
    background: rgba(0,0,0,0.7);
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    z-index: 1001;
    box-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.back-btn-area:hover .back-btn {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-btn:hover {
    background: rgba(0,0,0,0.9);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.4);
}

/* 专门针对1920*1080投屏的优化 */
@media screen and (width: 1920px) and (height: 1080px) {
    .screen-container {
        font-size: 16px; /* 基础字体大小 */
    }
    
    .screen-title {
        font-size: 3.5rem; /* 增大标题 */
    }
    
    .screen-time {
        font-size: 2.2rem;
    }
    
    /* 门店投屏优化 */
    .store-name-title {
        font-size: 2rem;  /* 与普通样式保持一致 */
    }
    
    .store-name-value {
        font-size: 5rem;  /* 与普通样式保持一致 */
    }
    
    .count-value {
        font-size: 6rem; /* 增大数值显示以适应新布局 */
    }
    
    .count-label {
        font-size: 1.6rem;  /* 增大标签字体 */
    }
    
    .progress-section .progress-text {
        font-size: 1.8rem;  /* 增大进度条文字 */
    }
    
    .bottom-info-label {
        font-size: 1.3rem;
    }
    
    .bottom-info-value {
        font-size: 1.6rem;
    }
    
    /* 线路投屏优化 */
    .route-count-card .count-value {
        font-size: 4.5rem;
    }
    
    .route-count-card .count-label {
        font-size: 1.4rem;
    }
    
    .route-info-label {
        font-size: 1.3rem;
    }
    
    .route-info-value {
        font-size: 1.6rem;
    }
    
    .route-info-row:first-child .route-info-value {
        font-size: 2rem; /* 倒计时更大 */
    }
    
    .route-stores-label {
        font-size: 5rem;
    }
    
    .route-store-item {
        font-size: 5rem;
    }
    
    /* 二维码优化 */
    .qr-section .qr-code {
        width: 540px;  /* 再放大35%：400*1.35=540px */
        height: 540px; /* 再放大35%：400*1.35=540px */
        border-radius: 45px;  /* 相应增大圆角 */
    }
    
    .route-qr-section .qr-code {
        width: 540px;  /* 增大二维码，适应新的区域大小 */
        height: 540px; /* 增大二维码，适应新的区域大小 */
        border-radius: 45px;  /* 相应增大圆角 */
    }
}

/* 响应式设计 */
@media screen and (max-width: 1600px) {
    .screen-title {
        font-size: 2.5rem;
    }
    
    .screen-time {
        font-size: 1.5rem;
    }
    
    .info-row {
        font-size: 1.3rem;
    }
    
    .countdown-timer {
        font-size: 2.5rem;
    }
    
    .qr-code {
        width: 150px;
        height: 150px;
    }
}

/* 门店投屏布局样式 */
.store-layout .store-main-content {
    flex: 1;
    display: flex;
    gap: 20px;
    padding: 10px;
    height: calc(1080px - 180px);
}

.left-main-section {
    flex: 1;
    background: #9acd32;
    border: 2px solid #333;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.store-name-section {
    flex: 0.5;  /* 减少50%高度 */
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border-bottom: 2px solid rgba(0, 255, 255, 0.5);
    padding: 15px;  /* 减少padding */
    background: linear-gradient(135deg, rgba(0, 50, 100, 0.4) 0%, rgba(0, 80, 150, 0.3) 100%);
    position: relative;
    box-shadow: 
        0 0 30px rgba(0, 255, 255, 0.2),
        inset 0 0 30px rgba(0, 255, 255, 0.1);
}

.store-name-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, 
        transparent 0%, 
        rgba(0, 255, 255, 0.1) 25%, 
        transparent 50%, 
        rgba(0, 255, 255, 0.1) 75%, 
        transparent 100%);
    pointer-events: none;
}

.store-name-title {
    font-size: 2rem;  /* 减小字体 */
    font-weight: bold;
    color: #66ccff;
    margin-bottom: 10px;  /* 减小间距 */
    text-shadow: 
        0 0 10px rgba(102, 204, 255, 0.8),
        0 0 20px rgba(102, 204, 255, 0.4);
    position: relative;
    z-index: 1;
}

.store-name-value {
    font-size: 8rem;  /* 减小字体 */
    font-weight: bold;
    color: #00ffff;
    text-align: center;
    text-shadow: 
        0 0 15px rgba(0, 255, 255, 0.8),
        0 0 30px rgba(0, 255, 255, 0.4);
    position: relative;
    z-index: 1;
}

.qr-section {
    flex: 1.5;  /* 增加二维码区域高度 */
    display: flex;
    align-items: center;
    justify-content: center;
}

.qr-section .qr-code {
    width: 550px;  /* 再放大35%：380*1.35=513px */
    height: 550px; /* 再放大35%：380*1.35=513px */
    background: white;
    border-radius: 40px;  /* 相应增大圆角 */
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 
        0 15px 40px rgba(0,0,0,0.5),
        0 0 35px rgba(0, 255, 255, 0.4);  /* 增强光晕效果 */
    border: 5px solid rgba(0, 255, 255, 0.5);  /* 增粗边框 */
}

.right-main-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.counts-section {
    flex: 1;  /* 平均分配空间 */
    display: flex;
    gap: 20px;
}

.count-card {
    flex: 1;
    border: 2px solid rgba(0, 255, 255, 0.5);
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
    box-shadow: 
        0 0 20px rgba(0, 255, 255, 0.2),
        inset 0 0 20px rgba(0, 255, 255, 0.1);
}

.sorted-card {
    background: linear-gradient(135deg, rgba(0, 50, 100, 0.4) 0%, rgba(0, 80, 150, 0.3) 100%);
}

.unsorted-card {
    background: linear-gradient(135deg, rgba(0, 50, 100, 0.4) 0%, rgba(0, 80, 150, 0.3) 100%);
}

.count-label {
    background: linear-gradient(135deg, rgba(0, 80, 150, 0.6) 0%, rgba(0, 50, 100, 0.4) 100%);
    padding: 20px 15px;  /* 增加上下padding */
    font-size: 2.4rem;  /* 增大字体 */
    font-weight: bold;
    text-align: center;
    border-bottom: 2px solid rgba(0, 255, 255, 0.5);
    color: #66ccff;
    text-shadow: 
        0 0 10px rgba(102, 204, 255, 0.8),
        0 0 20px rgba(102, 204, 255, 0.4);
}

.count-display-area {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(0, 100, 200, 0.6) 0%, rgba(0, 150, 255, 0.4) 100%);
    color: white;
    padding: 20px;
    position: relative;
}

.count-display-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, 
        transparent 0%, 
        rgba(0, 255, 255, 0.1) 25%, 
        transparent 50%, 
        rgba(0, 255, 255, 0.1) 75%, 
        transparent 100%);
    pointer-events: none;
}

.count-value {
    font-size: 7rem;  /* 增大数值字体 */
    font-weight: bold;
    color: #00ffff;
    text-shadow: 
        0 0 25px rgba(0, 255, 255, 0.8),
        0 0 50px rgba(0, 255, 255, 0.4);
    position: relative;
    z-index: 1;
    text-align: center;
    line-height: 1;
    display: block;
    width: 100%;
}

.progress-section {
    background: linear-gradient(135deg, rgba(0, 50, 100, 0.4) 0%, rgba(0, 80, 150, 0.3) 100%);
    border: 2px solid rgba(0, 255, 255, 0.5);
    border-radius: 10px;
    padding: 15px;  /* 减少padding */
    flex: 0.5;  /* 高度减少50% */
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    box-shadow: 
        0 0 20px rgba(0, 255, 255, 0.2),
        inset 0 0 20px rgba(0, 255, 255, 0.1);
}

.progress-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    background: linear-gradient(45deg, 
        transparent 0%, 
        rgba(0, 255, 255, 0.1) 25%, 
        transparent 50%, 
        rgba(0, 255, 255, 0.1) 75%, 
        transparent 100%);
    pointer-events: none;
}

.progress-label {
    font-size: 2.4rem;
    font-weight: bold;
    color: #66ccff;
    margin-bottom: 15px;
    text-shadow: 
        0 0 10px rgba(102, 204, 255, 0.8),
        0 0 20px rgba(102, 204, 255, 0.4);
    position: relative;
    z-index: 1;
}

.progress-section .progress-container {
    margin-top: 0;
    position: relative;
    z-index: 1;
}

.progress-section .progress-bar {
    height: 60px;  /* 减少进度条高度以适应压缩的区域 */
    background: rgba(0, 20, 40, 0.8);
    border-radius: 18px;  /* 相应调整圆角 */
    overflow: hidden;
    position: relative;
    border: 2px solid rgba(0, 255, 255, 0.5);
}

.progress-section .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00ffff, #0099ff, #00ccff);
    border-radius: 18px;  /* 相应调整圆角 */
    transition: width 0.5s ease;
    position: relative;
    box-shadow: 
        0 0 20px rgba(0, 255, 255, 0.6),
        inset 0 0 20px rgba(255, 255, 255, 0.1);
}

.progress-section .progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: bold;
    color: white;
    font-size: 3rem;  /* 相应减小字体以适应压缩的进度条 */
    z-index: 2;
    text-shadow: 
        0 1px 3px rgba(0, 0, 0, 0.8),
        0 0 5px rgba(0, 0, 0, 0.5);
    text-align: center;
    white-space: nowrap;
}

.bottom-info {
    height: 80px;
    display: flex;
    background: linear-gradient(135deg, rgba(0, 80, 150, 0.4) 0%, rgba(0, 50, 100, 0.6) 100%);
    border-top: 2px solid rgba(0, 255, 255, 0.3);
    backdrop-filter: blur(10px);
    position: relative;
}

.bottom-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, 
        transparent 0%, 
        rgba(0, 255, 255, 0.1) 25%, 
        transparent 50%, 
        rgba(0, 255, 255, 0.1) 75%, 
        transparent 100%);
    pointer-events: none;
}

.bottom-info-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    border-right: 1px solid rgba(0, 255, 255, 0.3);
    padding: 10px;
    position: relative;
    z-index: 1;
}

.bottom-info-item:last-child {
    border-right: none;
}

.bottom-info-label {
    font-size: 1.2rem;
    color: #66ccff;
    margin-right: 10px;
    text-shadow: 
        0 0 5px rgba(102, 204, 255, 0.8),
        0 0 10px rgba(102, 204, 255, 0.4);
}

.bottom-info-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: #00ffff;
    text-shadow: 
        0 0 10px rgba(0, 255, 255, 0.8),
        0 0 20px rgba(0, 255, 255, 0.4);
}

@media screen and (max-width: 1200px) {
    .screen-content {
        flex-direction: column;
    }
    
    .left-section {
        flex: none;
    }
    
    .right-section {
        flex: none;
        height: 300px;
    }
    
    .store-main-content {
        flex-direction: column;
        gap: 15px;
    }
    
    .counts-section {
        flex-direction: column;
        gap: 15px;
    }
    
    .count-value {
        font-size: 3rem;
    }
    
         .store-name-value {
         font-size: 2.5rem;
     }
}

/* 线路投屏布局样式 */
.route-layout .route-main-content {
    flex: 1;
    display: flex;
    gap: 20px;
    padding: 20px;
    height: calc(1080px - 180px);
}

.route-left-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.route-left-section .route-stores-container {
    flex: 0.5;  /* 缩小50%高度 */
    background: linear-gradient(135deg, rgba(0, 50, 100, 0.4) 0%, rgba(0, 80, 150, 0.3) 100%);
    border: 2px solid rgba(0, 255, 255, 0.5);
    border-radius: 10px;
    padding: 15px;
    position: relative;
    box-shadow: 
        0 0 20px rgba(0, 255, 255, 0.2),
        inset 0 0 20px rgba(0, 255, 255, 0.1);
}

.route-qr-section {
    flex: 1.5;  /* 增加高度，补充上侧缩小的空间 */
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(0, 50, 100, 0.4) 0%, rgba(0, 80, 150, 0.3) 100%);
    border: 2px solid rgba(0, 255, 255, 0.5);
    border-radius: 10px;
    position: relative;
    box-shadow: 
        0 0 20px rgba(0, 255, 255, 0.2),
        inset 0 0 20px rgba(0, 255, 255, 0.1);
}

.route-qr-section .qr-code {
    width: 550px;
    height: 550px;
    background: white;
    border-radius: 25px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 
        0 15px 40px rgba(0,0,0,0.5),
        0 0 35px rgba(0, 255, 255, 0.4);
    border: 5px solid rgba(0, 255, 255, 0.5);
}

.route-right-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.route-left-section .route-stores-label {
    font-size: 2.4rem;
    font-weight: bold;
    color: #66ccff;
    margin-bottom: 10px;
    text-align: center;
    text-shadow: 
        0 0 5px rgba(102, 204, 255, 0.8),
        0 0 10px rgba(102, 204, 255, 0.4);
}

.route-left-section .route-stores {
    flex: 1;
    overflow: hidden;
    position: relative;
    background: linear-gradient(135deg, rgba(0, 100, 200, 0.6) 0%, rgba(0, 150, 255, 0.4) 100%);
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 5px;
    padding: 10px;
    box-shadow: 
        inset 0 0 20px rgba(0, 255, 255, 0.1);
}

.route-counts-section {
    display: flex;
    gap: 20px;
    flex: 1;  /* 参考门店投屏的counts-section */
}

.route-count-card {
    flex: 1;
    border: 2px solid rgba(0, 255, 255, 0.5);
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: linear-gradient(135deg, rgba(0, 50, 100, 0.4) 0%, rgba(0, 80, 150, 0.3) 100%);
    position: relative;
    box-shadow: 
        0 0 20px rgba(0, 255, 255, 0.2),
        inset 0 0 20px rgba(0, 255, 255, 0.1);
}

.route-count-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    background: linear-gradient(45deg, 
        transparent 0%, 
        rgba(0, 255, 255, 0.1) 25%, 
        transparent 50%, 
        rgba(0, 255, 255, 0.1) 75%, 
        transparent 100%);
    pointer-events: none;
}

.route-count-card .count-label {
    background: linear-gradient(135deg, rgba(0, 80, 150, 0.6) 0%, rgba(0, 50, 100, 0.4) 100%);
    padding: 15px;
    font-size: 2.4rem;
    font-weight: bold;
    text-align: center;
    border-bottom: 2px solid rgba(0, 255, 255, 0.5);
    color: #66ccff;
    text-shadow: 
        0 0 10px rgba(102, 204, 255, 0.8),
        0 0 20px rgba(102, 204, 255, 0.4);
    position: relative;
    z-index: 1;
}

.route-count-card .count-display-area {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(0, 100, 200, 0.6) 0%, rgba(0, 150, 255, 0.4) 100%);
    color: white;
    padding: 20px;
    position: relative;
    z-index: 1;
}

.route-count-card .count-value {
    font-size: 6rem;
    font-weight: bold;
    color: #00ffff;
    text-shadow: 
        0 0 20px rgba(0, 255, 255, 0.8),
        0 0 40px rgba(0, 255, 255, 0.4);
    text-align: center;
    line-height: 1;
    display: block;
    width: 100%;
}

.route-progress-section {
    background: linear-gradient(135deg, rgba(0, 50, 100, 0.4) 0%, rgba(0, 80, 150, 0.3) 100%);
    border: 2px solid rgba(0, 255, 255, 0.5);
    border-radius: 10px;
    padding: 15px;  /* 减少padding */
    flex: 0.5;  /* 参考门店投屏的progress-section：高度减少50% */
    display: flex;
    flex-direction: column;
    justify-content: center;
    position: relative;
    box-shadow: 
        0 0 20px rgba(0, 255, 255, 0.2),
        inset 0 0 20px rgba(0, 255, 255, 0.1);
}

.route-progress-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 10px;
    background: linear-gradient(45deg, 
        transparent 0%, 
        rgba(0, 255, 255, 0.1) 25%, 
        transparent 50%, 
        rgba(0, 255, 255, 0.1) 75%, 
        transparent 100%);
    pointer-events: none;
}

.route-progress-section .progress-label {
    font-size: 2.4rem;
    font-weight: bold;
    color: #66ccff;
    margin-bottom: 15px;
    text-shadow: 
        0 0 10px rgba(102, 204, 255, 0.8),
        0 0 20px rgba(102, 204, 255, 0.4);
    position: relative;
    z-index: 1;
}

.route-progress-section .progress-container {
    margin-top: 0;
    position: relative;
    z-index: 1;
}

.route-progress-section .progress-bar {
    height: 60px;
    background: rgba(0, 20, 40, 0.8);
    border-radius: 20px;
    overflow: hidden;
    position: relative;
    border: 2px solid rgba(0, 255, 255, 0.5);
}

.route-progress-section .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00ffff, #0099ff, #00ccff);
    border-radius: 20px;
    transition: width 0.5s ease;
    position: relative;
    box-shadow: 
        0 0 20px rgba(0, 255, 255, 0.6),
        inset 0 0 20px rgba(255, 255, 255, 0.1);
}

.route-progress-section .progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: bold;
    color: white;
    font-size: 3rem;
    z-index: 2;
    text-shadow: 
        0 1px 3px rgba(0, 0, 0, 0.8),
        0 0 5px rgba(0, 0, 0, 0.5);
    text-align: center;
    white-space: nowrap;
}

.route-bottom-info {
    flex: 1.5;  /* 参考门店投屏的store-bottom-info：增加空间，获得进度区域减少的50% */
    display: flex;
    flex-direction: column;
    gap: 20px;  /* 增加间距 */
}

.route-info-row {
    background: linear-gradient(135deg, rgba(0, 50, 100, 0.4) 0%, rgba(0, 80, 150, 0.3) 100%);
    border: 2px solid rgba(0, 255, 255, 0.5);
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    flex: 1;  /* 每行平均分配高度 */
    position: relative;
    box-shadow: 
        0 0 15px rgba(0, 255, 255, 0.2),
        inset 0 0 15px rgba(0, 255, 255, 0.1);
}

.route-info-row::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 5px;
    background: linear-gradient(45deg, 
        transparent 0%, 
        rgba(0, 255, 255, 0.1) 25%, 
        transparent 50%, 
        rgba(0, 255, 255, 0.1) 75%, 
        transparent 100%);
    pointer-events: none;
}

.route-info-label {
    font-size: 2.4rem;
    color: #66ccff;
    font-weight: bold;
    text-shadow: 
        0 0 5px rgba(102, 204, 255, 0.8),
        0 0 10px rgba(102, 204, 255, 0.4);
    position: relative;
    z-index: 1;
}

.route-info-value {
    font-size: 2.4rem;
    font-weight: bold;
    color: #00ffff;
    position: relative;
    z-index: 1;
}

/* 特殊样式调整 */
.route-info-row:first-child .route-info-value {
    color: #ff9900;
    font-size: 5rem;
}

.route-info-row:nth-child(2) .route-info-value {
    color: #0099ff;
    text-shadow: 
        0 0 10px rgba(0, 153, 255, 0.8),
        0 0 20px rgba(0, 153, 255, 0.4);
}

.route-info-row:last-child .route-info-value {
    color: #00ffff;
    text-shadow: 
        0 0 10px rgba(0, 255, 255, 0.8),
        0 0 20px rgba(0, 255, 255, 0.4);
}

@media screen and (max-width: 1200px) {
    .route-main-content {
        flex-direction: column;
        gap: 15px;
        height: auto;
    }
    
    .route-counts-section {
        flex-direction: row;
        gap: 15px;
        height: auto;
    }
    
    .route-count-card .count-value {
        font-size: 2.5rem;
    }
    
    .route-qr-section .qr-code {
        width: 280px;
        height: 280px;
    }
    
    .route-info-row {
        padding: 10px 15px;
        height: auto;
        min-height: 40px;
    }
    
    .route-info-value {
        font-size: 1.1rem;
    }
    
    .route-info-row:first-child .route-info-value {
        font-size: 1.3rem;
    }
    
    .route-left-section .route-stores-container {
        height: 180px;
    }
    
    .route-stores-label {
        font-size: 0.9rem;
    }
    
    .route-store-item {
        font-size: 0.9rem;
        padding: 6px 10px;
        margin: 4px 0;
    }
}

/* Store底部信息样式 */
.store-bottom-info {
    flex: 1.5;  /* 增加空间，获得进度区域减少的50% */
    display: flex;
    flex-direction: column;
    gap: 20px;  /* 增加间距 */
}

.store-info-row {
    background: linear-gradient(135deg, rgba(0, 50, 100, 0.4) 0%, rgba(0, 80, 150, 0.3) 100%);
    border: 2px solid rgba(0, 255, 255, 0.5);
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    flex: 1;  /* 每行平均分配高度 */
    position: relative;
    box-shadow: 
        0 0 15px rgba(0, 255, 255, 0.2),
        inset 0 0 15px rgba(0, 255, 255, 0.1);
}

.store-info-row::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 5px;
    background: linear-gradient(45deg, 
        transparent 0%, 
        rgba(0, 255, 255, 0.1) 25%, 
        transparent 50%, 
        rgba(0, 255, 255, 0.1) 75%, 
        transparent 100%);
    pointer-events: none;
}

.store-info-label {
    font-size: 2.4rem;  /* 字体放大50%：1.6*1.5=2.4rem */
    color: #66ccff;
    font-weight: bold;
    text-shadow: 
        0 0 8px rgba(102, 204, 255, 0.8),
        0 0 15px rgba(102, 204, 255, 0.4);
    position: relative;
    z-index: 1;
}

.store-info-value {
    font-size: 2.7rem;  /* 字体放大50%：1.8*1.5=2.7rem */
    font-weight: bold;
    color: #00ffff;
    position: relative;
    z-index: 1;
}

/* Store特殊样式调整 */
.store-info-row:first-child .store-info-value {
    color: #ff9900;
    font-size: 5rem;  /* 倒计时字体放大50%：2.2*1.5=3.3rem */
}

.store-info-row:nth-child(2) .store-info-value {
    color: #0099ff;
    text-shadow: 
        0 0 10px rgba(0, 153, 255, 0.8),
        0 0 20px rgba(0, 153, 255, 0.4);
}

.store-info-row:last-child .store-info-value {
    color: #00ffff;
    text-shadow: 
        0 0 10px rgba(0, 255, 255, 0.8),
        0 0 20px rgba(0, 255, 255, 0.4);
}

@media screen and (max-width: 1200px) {
    .store-info-row {
        padding: 10px 15px;
        height: 40px;
    }
    
    .store-info-value {
        font-size: 1.2rem;
    }
    
    .store-info-row:first-child .store-info-value {
        font-size: 1.4rem;
    }
}

@media (max-width: 600px) {
    .container {
        width: 98%;
        padding: 1rem 0.2rem;
    }
    .form-container {
        padding: 1.2rem 0.5rem;
        border-radius: 0.4rem;
    }
    .header h1 {
        font-size: 1.3rem;
    }
    .btn {
        font-size: 1rem;
        padding: 1.2rem 0.5rem;
    }
}

/* 全屏/投屏按钮样式 */
.fullscreen-btn {
    position: absolute;
    top: 10px;
    right: 32px;
    padding: 8px 24px;
    background: linear-gradient(225deg, #6EA9FE 0%, #817BFE 100%);
    color: #fff;
    border: none;
    border-radius: 20px;
    font-size: 16px;
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(130, 130, 255, 0.08);
    cursor: pointer;
    transition: background 0.2s, box-shadow 0.2s;
    z-index: 10;
}
.fullscreen-btn:hover {
    background: linear-gradient(225deg, #5a8de0 0%, #6a5bfe 100%);
    box-shadow: 0 4px 16px rgba(130, 130, 255, 0.16);
} 